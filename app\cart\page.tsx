"use client"
import { useState } from "react"
import { useRouter } from "next/navigation"
import { Navbar } from "@/components/Navbar"
import { CartItem } from "@/components/CartItem"
import { ProtectedRoute } from "@/components/ProtectedRoute"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { useCart } from "@/context/CartContext"
import { ShoppingBag, ArrowLeft, Truck, Shield, CreditCard } from "lucide-react"
import Link from "next/link"

export default function CartPage() {
  const { items, totalItems, totalPrice, loading } = useCart()
  const router = useRouter()
  const [processingCheckout, setProcessingCheckout] = useState(false)

  const shippingCost = totalPrice >= 50 ? 0 : 9.99
  const tax = totalPrice * 0.08 // 8% tax
  const finalTotal = totalPrice + shippingCost + tax

  const handleCheckout = async () => {
    setProcessingCheckout(true)
    // Simulate checkout process
    setTimeout(() => {
      router.push("/checkout")
      setProcessingCheckout(false)
    }, 1000)
  }

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gray-50">
          <Navbar />
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="h-24 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <Navbar />

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Shopping Cart</h1>
              <p className="text-gray-600 mt-2">
                {totalItems} item{totalItems !== 1 ? "s" : ""} in your cart
              </p>
            </div>
            <Button variant="outline" onClick={() => router.push("/products")}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Continue Shopping
            </Button>
          </div>

          {items.length === 0 ? (
            /* Empty Cart */
            <div className="text-center py-12">
              <ShoppingBag className="h-24 w-24 text-gray-300 mx-auto mb-6" />
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Your cart is empty</h2>
              <p className="text-gray-600 mb-8">Looks like you haven't added any eco-friendly products yet.</p>
              <Button asChild>
                <Link href="/products">Start Shopping</Link>
              </Button>
            </div>
          ) : (
            <div className="grid lg:grid-cols-3 gap-8">
              {/* Cart Items */}
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Cart Items</CardTitle>
                  </CardHeader>
                  <CardContent className="p-0">
                    <div className="divide-y divide-gray-200">
                      {items.map((item) => (
                        <div key={item.id} className="px-6">
                          <CartItem item={item} />
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Shipping Benefits */}
                <Card className="mt-6">
                  <CardContent className="pt-6">
                    <div className="grid md:grid-cols-3 gap-4">
                      <div className="flex items-center space-x-3">
                        <Truck className="h-8 w-8 text-green-600" />
                        <div>
                          <h4 className="font-medium">Free Shipping</h4>
                          <p className="text-sm text-gray-600">On orders over $50</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Shield className="h-8 w-8 text-green-600" />
                        <div>
                          <h4 className="font-medium">Secure Payment</h4>
                          <p className="text-sm text-gray-600">SSL encrypted checkout</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <CreditCard className="h-8 w-8 text-green-600" />
                        <div>
                          <h4 className="font-medium">Easy Returns</h4>
                          <p className="text-sm text-gray-600">30-day return policy</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Order Summary */}
              <div className="lg:col-span-1">
                <Card className="sticky top-8">
                  <CardHeader>
                    <CardTitle>Order Summary</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between">
                      <span>Subtotal ({totalItems} items)</span>
                      <span>${totalPrice.toFixed(2)}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="flex items-center">
                        Shipping
                        {shippingCost === 0 && (
                          <Badge variant="secondary" className="ml-2 text-xs">
                            FREE
                          </Badge>
                        )}
                      </span>
                      <span>${shippingCost.toFixed(2)}</span>
                    </div>

                    <div className="flex justify-between">
                      <span>Tax</span>
                      <span>${tax.toFixed(2)}</span>
                    </div>

                    <Separator />

                    <div className="flex justify-between text-lg font-semibold">
                      <span>Total</span>
                      <span>${finalTotal.toFixed(2)}</span>
                    </div>

                    {totalPrice < 50 && (
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <p className="text-sm text-blue-800">
                          Add ${(50 - totalPrice).toFixed(2)} more to get free shipping!
                        </p>
                      </div>
                    )}

                    <Button className="w-full" onClick={handleCheckout} disabled={processingCheckout}>
                      {processingCheckout ? "Processing..." : "Proceed to Checkout"}
                    </Button>

                    <div className="text-center">
                      <Link href="/products" className="text-sm text-green-600 hover:text-green-500">
                        Continue shopping
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}
        </div>
      </div>
    </ProtectedRoute>
  )
}
