"use client"

import type React from "react"
import { useState } from "react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useCart } from "@/context/CartContext"
import { useToast } from "@/hooks/use-toast"
import { Trash2, Plus, Minus, Loader2 } from "lucide-react"

interface CartItemProps {
  item: {
    id: string
    productId: string
    name: string
    price: number
    quantity: number
    image: string
  }
}

export const CartItem: React.FC<CartItemProps> = ({ item }) => {
  const [loading, setLoading] = useState(false)
  const [quantityInput, setQuantityInput] = useState(item.quantity.toString())
  const { updateQuantity, removeFromCart } = useCart()
  const { toast } = useToast()

  const handleQuantityChange = async (newQuantity: number) => {
    if (newQuantity < 1) return

    setLoading(true)
    try {
      await updateQuantity(item.productId, newQuantity)
      setQuantityInput(newQuantity.toString())
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update quantity. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleQuantityInputChange = (value: string) => {
    setQuantityInput(value)
    const numValue = Number.parseInt(value)
    if (!isNaN(numValue) && numValue > 0) {
      handleQuantityChange(numValue)
    }
  }

  const handleRemove = async () => {
    setLoading(true)
    try {
      await removeFromCart(item.productId)
      toast({
        title: "Item removed",
        description: `${item.name} has been removed from your cart.`,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to remove item. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="flex items-center space-x-4 py-6 border-b border-gray-200">
      {/* Product Image */}
      <div className="flex-shrink-0">
        <Link href={`/products/${item.productId}`}>
          <img
            src={item.image || `/placeholder.svg?height=100&width=100&query=${item.name}`}
            alt={item.name}
            className="w-20 h-20 object-cover rounded-lg hover:opacity-75 transition-opacity"
          />
        </Link>
      </div>

      {/* Product Info */}
      <div className="flex-1 min-w-0">
        <Link href={`/products/${item.productId}`} className="hover:text-green-600 transition-colors">
          <h3 className="text-lg font-medium text-gray-900 truncate">{item.name}</h3>
        </Link>
        <p className="text-2xl font-bold text-green-600 mt-1">${item.price.toFixed(2)}</p>
      </div>

      {/* Quantity Controls */}
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleQuantityChange(item.quantity - 1)}
          disabled={loading || item.quantity <= 1}
        >
          <Minus className="h-4 w-4" />
        </Button>
        <Input
          type="number"
          min="1"
          value={quantityInput}
          onChange={(e) => handleQuantityInputChange(e.target.value)}
          className="w-16 text-center"
          disabled={loading}
        />
        <Button variant="outline" size="sm" onClick={() => handleQuantityChange(item.quantity + 1)} disabled={loading}>
          <Plus className="h-4 w-4" />
        </Button>
      </div>

      {/* Subtotal */}
      <div className="text-right">
        <p className="text-lg font-semibold text-gray-900">${(item.price * item.quantity).toFixed(2)}</p>
      </div>

      {/* Remove Button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={handleRemove}
        disabled={loading}
        className="text-red-600 hover:text-red-700"
      >
        {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Trash2 className="h-4 w-4" />}
      </Button>
    </div>
  )
}
