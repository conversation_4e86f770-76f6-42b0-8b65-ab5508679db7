"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect, type ReactNode } from "react"
import { cartService } from "@/services/cartService"
import { useAuth } from "./AuthContext"

interface CartItem {
  id: string
  productId: string
  name: string
  price: number
  quantity: number
  image: string
}

interface CartContextType {
  items: CartItem[]
  addToCart: (productId: string, quantity?: number) => Promise<void>
  removeFromCart: (productId: string) => Promise<void>
  updateQuantity: (productId: string, quantity: number) => Promise<void>
  clearCart: () => void
  totalItems: number
  totalPrice: number
  loading: boolean
}

const CartContext = createContext<CartContextType | undefined>(undefined)

export const useCart = () => {
  const context = useContext(CartContext)
  if (context === undefined) {
    throw new Error("useCart must be used within a CartProvider")
  }
  return context
}

interface CartProviderProps {
  children: ReactNode
}

export const CartProvider: React.FC<CartProviderProps> = ({ children }) => {
  const [items, setItems] = useState<CartItem[]>([])
  const [loading, setLoading] = useState(false)
  const { isAuthenticated } = useAuth()

  useEffect(() => {
    if (isAuthenticated) {
      loadCart()
    } else {
      setItems([])
    }
  }, [isAuthenticated])

  const loadCart = async () => {
    try {
      setLoading(true)
      const cartData = await cartService.getCart()
      setItems(cartData.items || [])
    } catch (error) {
      console.error("Failed to load cart:", error)
    } finally {
      setLoading(false)
    }
  }

  const addToCart = async (productId: string, quantity = 1) => {
    try {
      await cartService.addToCart(productId, quantity)
      await loadCart()
    } catch (error) {
      console.error("Failed to add to cart:", error)
    }
  }

  const removeFromCart = async (productId: string) => {
    try {
      await cartService.removeFromCart(productId)
      await loadCart()
    } catch (error) {
      console.error("Failed to remove from cart:", error)
    }
  }

  const updateQuantity = async (productId: string, quantity: number) => {
    try {
      await cartService.updateQuantity(productId, quantity)
      await loadCart()
    } catch (error) {
      console.error("Failed to update quantity:", error)
    }
  }

  const clearCart = () => {
    setItems([])
  }

  const totalItems = items.reduce((sum, item) => sum + item.quantity, 0)
  const totalPrice = items.reduce((sum, item) => sum + item.price * item.quantity, 0)

  const value: CartContextType = {
    items,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    totalItems,
    totalPrice,
    loading,
  }

  return <CartContext.Provider value={value}>{children}</CartContext.Provider>
}
