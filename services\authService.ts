import { api } from "./api"

interface LoginResponse {
  token: string
  user: {
    id: string
    email: string
    name: string
    role: "user" | "admin"
  }
}

interface RegisterResponse {
  token: string
  user: {
    id: string
    email: string
    name: string
    role: "user" | "admin"
  }
}

const mockUsers = [
  { id: "1", email: "<EMAIL>", password: "admin123", name: "Admin User", role: "admin" as const },
  { id: "2", email: "<EMAIL>", password: "user123", name: "Regular User", role: "user" as const },
]

export const authService = {
  async login(email: string, password: string): Promise<LoginResponse> {
    try {
      const response = await api.post("/api/auth/login", { email, password })
      return response.data
    } catch (error) {
      console.log("[v0] API login failed, using mock authentication")

      const mockUser = mockUsers.find((u) => u.email === email && u.password === password)
      if (mockUser) {
        return {
          token: `mock-token-${mockUser.id}-${Date.now()}`,
          user: {
            id: mockUser.id,
            email: mockUser.email,
            name: mockUser.name,
            role: mockUser.role,
          },
        }
      }
      throw new Error("Invalid credentials")
    }
  },

  async register(name: string, email: string, password: string): Promise<RegisterResponse> {
    try {
      const response = await api.post("/api/auth/register", { name, email, password })
      return response.data
    } catch (error) {
      console.log("[v0] API register failed, using mock registration")

      const existingUser = mockUsers.find((u) => u.email === email)
      if (existingUser) {
        throw new Error("User already exists")
      }

      const newUser = {
        id: `mock-${Date.now()}`,
        email,
        name,
        role: "user" as const,
      }

      // Add to mock users for future logins
      mockUsers.push({ ...newUser, password })

      return {
        token: `mock-token-${newUser.id}-${Date.now()}`,
        user: newUser,
      }
    }
  },

  async getCurrentUser() {
    try {
      const response = await api.get("/api/auth/me")
      return response.data
    } catch (error) {
      console.log("[v0] API getCurrentUser failed, using mock user data")

      const token = localStorage.getItem("token")
      if (token && token.startsWith("mock-token-")) {
        const userId = token.split("-")[2]
        const mockUser = mockUsers.find((u) => u.id === userId)
        if (mockUser) {
          return {
            id: mockUser.id,
            email: mockUser.email,
            name: mockUser.name,
            role: mockUser.role,
          }
        }
      }
      throw new Error("Invalid token")
    }
  },
}
