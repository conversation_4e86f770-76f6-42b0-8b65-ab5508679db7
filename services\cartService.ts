import { api } from "./api"
import { productService } from "./productService"

interface CartItem {
  id: string
  productId: string
  name: string
  price: number
  quantity: number
  image: string
}

interface Cart {
  items: CartItem[]
  totalItems: number
  totalPrice: number
}

let mockCart: Cart = {
  items: [],
  totalItems: 0,
  totalPrice: 0,
}

export const cartService = {
  async getCart(): Promise<Cart> {
    try {
      const response = await api.get("/api/cart")
      return response.data
    } catch (error) {
      console.log("[v0] Cart API failed, using mock cart:", error)
      return mockCart
    }
  },

  async addToCart(productId: string, quantity: number): Promise<void> {
    try {
      await api.post("/api/cart", { productId, quantity })
    } catch (error) {
      console.log("[v0] Add to cart API failed, using mock cart:", error)
      const products = await productService.getProducts()
      const product = products.find((p) => p.id === productId)

      if (product) {
        const existingItem = mockCart.items.find((item) => item.productId === productId)

        if (existingItem) {
          existingItem.quantity += quantity
        } else {
          mockCart.items.push({
            id: `cart-${Date.now()}`,
            productId: product.id,
            name: product.name,
            price: product.price,
            quantity,
            image: product.images[0] || "/placeholder.svg",
          })
        }

        mockCart.totalItems = mockCart.items.reduce((sum, item) => sum + item.quantity, 0)
        mockCart.totalPrice = mockCart.items.reduce((sum, item) => sum + item.price * item.quantity, 0)
      }
    }
  },

  async updateQuantity(productId: string, quantity: number): Promise<void> {
    try {
      await api.put("/api/cart", { productId, quantity })
    } catch (error) {
      console.log("[v0] Update quantity API failed, using mock cart:", error)
      const item = mockCart.items.find((item) => item.productId === productId)
      if (item) {
        if (quantity <= 0) {
          mockCart.items = mockCart.items.filter((item) => item.productId !== productId)
        } else {
          item.quantity = quantity
        }

        mockCart.totalItems = mockCart.items.reduce((sum, item) => sum + item.quantity, 0)
        mockCart.totalPrice = mockCart.items.reduce((sum, item) => sum + item.price * item.quantity, 0)
      }
    }
  },

  async removeFromCart(productId: string): Promise<void> {
    try {
      await api.delete(`/api/cart/${productId}`)
    } catch (error) {
      console.log("[v0] Remove from cart API failed, using mock cart:", error)
      mockCart.items = mockCart.items.filter((item) => item.productId !== productId)

      mockCart.totalItems = mockCart.items.reduce((sum, item) => sum + item.quantity, 0)
      mockCart.totalPrice = mockCart.items.reduce((sum, item) => sum + item.price * item.quantity, 0)
    }
  },

  async clearCart(): Promise<void> {
    try {
      await api.delete("/api/cart")
    } catch (error) {
      console.log("[v0] Clear cart API failed, using mock cart:", error)
      mockCart = {
        items: [],
        totalItems: 0,
        totalPrice: 0,
      }
    }
  },
}
