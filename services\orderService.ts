import { api } from "./api"

interface OrderItem {
  id: string
  productId: string
  name: string
  price: number
  quantity: number
  image: string
}

export interface Order {
  id: string
  items: OrderItem[]
  totalAmount: number
  status: "pending" | "processing" | "shipped" | "delivered" | "cancelled"
  createdAt: string
  shippingAddress: string
}

export const orderService = {
  async getOrders(): Promise<Order[]> {
    const response = await api.get("/api/orders")
    return response.data
  },

  async getOrder(id: string): Promise<Order> {
    const response = await api.get(`/api/orders/${id}`)
    return response.data
  },

  async createOrder(orderData: { items: OrderItem[]; shippingAddress: string }): Promise<Order> {
    const response = await api.post("/api/orders", orderData)
    return response.data
  },
}
