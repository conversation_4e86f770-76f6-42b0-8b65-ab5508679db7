import { api } from "./api"

export interface Product {
  id: string
  name: string
  description: string
  price: number
  category: string
  image: string
  images?: string[]
  inStock: boolean
  createdAt: string
}

const mockProducts: Product[] = [
  {
    id: "1",
    name: "Bamboo Water Bottle",
    description:
      "Sustainable bamboo water bottle with stainless steel interior. Perfect for staying hydrated while reducing plastic waste.",
    price: 24.99,
    category: "personal",
    image: "/bamboo-water-bottle-eco-friendly.jpg",
    images: ["/bamboo-water-bottle-eco-friendly.jpg", "/bamboo-water-bottle-interior.jpg"],
    inStock: true,
    createdAt: "2024-01-15T10:00:00Z",
  },
  {
    id: "2",
    name: "Organic Cotton Tote Bag",
    description: "Durable organic cotton tote bag for all your shopping needs. Say goodbye to plastic bags forever.",
    price: 18.5,
    category: "personal",
    image: "/organic-cotton-tote-bag-eco-friendly.jpg",
    inStock: true,
    createdAt: "2024-01-14T09:30:00Z",
  },
  {
    id: "3",
    name: "Solar Phone Charger",
    description: "Portable solar-powered phone charger. Harness the power of the sun to keep your devices charged.",
    price: 45.0,
    category: "electronics",
    image: "/placeholder-zktxs.png",
    inStock: true,
    createdAt: "2024-01-13T14:20:00Z",
  },
  {
    id: "4",
    name: "Beeswax Food Wraps",
    description: "Set of 3 reusable beeswax food wraps. Natural alternative to plastic wrap for food storage.",
    price: 16.99,
    category: "kitchen",
    image: "/placeholder-bz4qm.png",
    inStock: true,
    createdAt: "2024-01-12T11:45:00Z",
  },
  {
    id: "5",
    name: "Recycled Yoga Mat",
    description: "High-quality yoga mat made from recycled materials. Non-slip surface for all your yoga practices.",
    price: 32.0,
    category: "personal",
    image: "/placeholder-zamhn.png",
    inStock: false,
    createdAt: "2024-01-11T16:10:00Z",
  },
  {
    id: "6",
    name: "Compost Bin",
    description: "Compact kitchen compost bin with charcoal filter. Turn your food scraps into garden gold.",
    price: 28.75,
    category: "garden",
    image: "/placeholder-jdmk6.png",
    inStock: true,
    createdAt: "2024-01-10T08:15:00Z",
  },
  {
    id: "7",
    name: "Hemp Backpack",
    description: "Durable hemp backpack with multiple compartments. Perfect for work, school, or outdoor adventures.",
    price: 55.0,
    category: "personal",
    image: "/placeholder-3ac6l.png",
    inStock: true,
    createdAt: "2024-01-09T13:30:00Z",
  },
  {
    id: "8",
    name: "LED Plant Grow Light",
    description: "Energy-efficient LED grow light for indoor plants. Help your plants thrive year-round.",
    price: 39.99,
    category: "garden",
    image: "/placeholder-8w7mu.png",
    inStock: true,
    createdAt: "2024-01-08T12:00:00Z",
  },
]

export const productService = {
  async getProducts(category?: string, search?: string): Promise<Product[]> {
    try {
      console.log("[v0] Attempting to fetch products from API...")
      const params = new URLSearchParams()
      if (category) params.append("category", category)
      if (search) params.append("search", search)

      const response = await api.get(`/api/products?${params.toString()}`)
      console.log("[v0] Successfully fetched products from API")
      return response.data
    } catch (error) {
      console.log("[v0] API call failed, using mock data:", error)
      let filtered = [...mockProducts]

      if (category) {
        filtered = filtered.filter((product) => product.category === category)
      }

      if (search) {
        const searchLower = search.toLowerCase()
        filtered = filtered.filter(
          (product) =>
            product.name.toLowerCase().includes(searchLower) || product.description.toLowerCase().includes(searchLower),
        )
      }

      return filtered
    }
  },

  async getProduct(id: string): Promise<Product> {
    try {
      console.log("[v0] Attempting to fetch product from API...")
      const response = await api.get(`/api/products/${id}`)
      console.log("[v0] Successfully fetched product from API")
      return response.data
    } catch (error) {
      console.log("[v0] API call failed, using mock data:", error)
      const product = mockProducts.find((p) => p.id === id)
      if (!product) {
        throw new Error("Product not found")
      }
      return product
    }
  },

  async createProduct(productData: Omit<Product, "id" | "createdAt">): Promise<Product> {
    const response = await api.post("/api/products", productData)
    return response.data
  },

  async updateProduct(id: string, productData: Partial<Product>): Promise<Product> {
    const response = await api.put(`/api/products/${id}`, productData)
    return response.data
  },

  async deleteProduct(id: string): Promise<void> {
    await api.delete(`/api/products/${id}`)
  },
}
